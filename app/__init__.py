from fastapi import FastAP<PERSON>, responses
from app.v1.api import v1_api  # must be a FastAPI instance, not just an APIRouter

app = FastAPI(
    title="Main App",
    description="This is the main app. Sub-apps like /v1 are mounted separately.",
    version="main",
    openapi_version="3.1.0",
    servers=[
        {"url": "/", "description": "Main Server"},
        {"url": "/v1", "description": "API v1"}
    ]
)

# Mount versioned sub-apps
app.mount("/v1", v1_api)

@app.get("/", include_in_schema=False)
async def root():
    return responses.RedirectResponse(url="/docs")

@app.get("/hello", tags=["Main"])
async def hello_main():
    return {"message": "Hello from Main App"}
@app.on_event("startup")
async def on_startup():
    print("🚀 Server starting up...")

@app.on_event("shutdown")
async def on_shutdown():
    print("👋 Server shutting down...")