"""
Flow-Preserving Nepali Legal Document Chunker

This chunker maintains the natural flow and narrative of legal documents
while creating meaningful chunks that preserve context and readability.
"""

import re
from typing import List, Dict, Any, Optional
from pydantic import Field
from llama_index.core.schema import TransformComponent, NodeWithScore, TextNode


class FlowPreservingNepaliChunker(TransformComponent):
    """
    Semantic chunker for Nepali legal documents that preserves text integrity.

    Features:
    - Semantic chunking based on document structure
    - Preserves original text without modification
    - Creates fewer, larger chunks with meaningful boundaries
    - Intelligent overlap for context preservation
    - Respects legal document patterns
    """

    max_tokens: int = Field(default=1500, description="Maximum tokens per chunk")
    min_chunk_size: int = Field(default=500, description="Minimum tokens per chunk")
    overlap_sentences: int = Field(default=3, description="Number of sentences to overlap")
    preserve_flow: bool = Field(default=True, description="Maintain document flow")
    
    def __call__(self, node: TextNode) -> tuple[List[NodeWithScore], Dict[str, Any]]:
        """Main entry point for transformation"""
        return self.transform_single_text_node(node)
    
    def transform_single_text_node(self, node: TextNode) -> tuple[List[NodeWithScore], Dict[str, Any]]:
        """Transform text while preserving flow"""

        # Clean and prepare text
        cleaned_text = self._clean_text(node.text)

        # Split into semantic sentences while preserving flow
        sentences = self._split_into_flow_sentences(cleaned_text)

        if not sentences:
            return [], {"error": "No valid sentences found"}

        # Create flow-preserving chunks
        chunks = self._create_flow_chunks(sentences)

        # Convert to nodes
        new_nodes = []
        for i, chunk_text in enumerate(chunks):
            chunk_metadata = node.metadata.copy() if node.metadata else {}
            chunk_metadata.update({
                "chunk_id": i,
                "total_chunks": len(chunks),
                "chunk_tokens": len(chunk_text),
                "chunking_method": "flow_preserving",
                "flow_position": self._determine_flow_position(i, len(chunks))
            })

            chunk_node = TextNode(text=chunk_text, metadata=chunk_metadata)
            new_nodes.append(NodeWithScore(node=chunk_node, score=None))

        # Calculate statistics
        stats = {
            "total_tokens": len(cleaned_text),
            "total_sentences": len(sentences),
            "chunks_created": len(chunks),
            "avg_tokens_per_chunk": round(sum(len(c) for c in chunks) / len(chunks), 2) if chunks else 0,
            "flow_quality": self._calculate_flow_quality(chunks),
            "semantic_boundaries": sum(1 for chunk in chunks if chunk.rstrip().endswith(('।', '?', '!'))),
            "chunking_efficiency": round(len(chunks) / (len(cleaned_text) / self.max_tokens), 2) if chunks else 0
        }
        
        return new_nodes, stats
    
    def _clean_text(self, text: str) -> str:
        """Minimal cleaning to preserve original text structure"""
        # Only normalize excessive whitespace while preserving structure
        text = re.sub(r'[ \t]+', ' ', text)  # Multiple spaces/tabs to single space
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)  # Multiple line breaks to double

        # Clean only obvious HTML entities that don't affect content
        text = re.sub(r'&nbsp;', ' ', text)

        # Remove only clear page markers that don't affect content
        text = re.sub(r'५२२-->', '', text)

        return text.strip()

    def _split_into_flow_sentences(self, text: str) -> List[str]:
        """Split text into semantic units based on document structure"""

        # Split by paragraphs first to maintain document structure
        paragraphs = text.split('\n\n')

        sentences = []
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # Check if this is a structural element (header, case number, etc.)
            if self._is_structural_element(paragraph):
                sentences.append(paragraph)
                continue

            # For content paragraphs, split by semantic boundaries
            # Split by major punctuation but keep longer segments
            parts = re.split(r'([।!?])', paragraph)

            current_sentence = ""
            i = 0
            while i < len(parts):
                part = parts[i].strip()

                if not part:
                    i += 1
                    continue

                if part in ['।', '!', '?']:
                    current_sentence += part
                    # Only split if we have a substantial sentence
                    if len(current_sentence.strip()) > 100:
                        sentences.append(current_sentence.strip())
                        current_sentence = ""
                    else:
                        current_sentence += " "  # Continue building
                else:
                    if current_sentence and not current_sentence.endswith(' '):
                        current_sentence += " "
                    current_sentence += part

                i += 1

            # Add any remaining content
            if current_sentence.strip():
                sentences.append(current_sentence.strip())

        # Filter valid sentences
        return [s for s in sentences if self._is_valid_semantic_unit(s)]
    
    def _is_structural_element(self, text: str) -> bool:
        """Check if text is a structural element (header, case number, etc.)"""
        text = text.strip()

        # Case numbers and legal references
        if re.match(r'^\d{3}-[A-Z]{2}-\d{4}$', text):
            return True

        # Court names and titles
        if re.match(r'^(सर्वोच्च|उच्च|जिल्ला)\s+अदालत', text):
            return True

        # Judge names and titles
        if re.match(r'^(सम्माननीय|माननीय|प्र\.न्या\.)', text):
            return True

        # Section headers
        if re.match(r'^(मुद्दा|फैसला|निर्णय|भाग|अंक)', text):
            return True

        # Party information
        if re.match(r'^(पुनरावेदक|प्रत्यर्थी|वादी|प्रतिवादी)', text):
            return True

        # Short administrative text
        if len(text) < 50 and re.search(r'(मिति|नं\.|तर्फबाट)', text):
            return True

        return False

    def _is_valid_semantic_unit(self, text: str) -> bool:
        """Check if text is a valid semantic unit for chunking"""
        text = text.strip()

        # Minimum length
        if len(text) < 15:
            return False

        # Skip pure punctuation or numbers
        if re.match(r'^[।\.\-\s,;:()\d]+$', text):
            return False

        # Skip empty HTML entities
        if text in ['&nbsp;', '&amp;', '&lt;', '&gt;']:
            return False

        return True

    def _create_flow_chunks(self, sentences: List[str]) -> List[str]:
        """Create semantic chunks with intelligent boundaries"""

        if not sentences:
            return []

        chunks = []
        current_chunk = []
        current_tokens = 0

        i = 0
        while i < len(sentences):
            sentence = sentences[i]
            sentence_tokens = len(sentence)

            # Always try to include more content before breaking
            would_exceed = current_tokens + sentence_tokens > self.max_tokens

            if would_exceed and current_chunk:
                # Check if this is a natural breaking point
                if self._is_semantic_boundary(current_chunk, sentences, i):
                    # Create chunk with current content
                    chunk_text = self._join_sentences_naturally(current_chunk)
                    if len(chunk_text) >= self.min_chunk_size:
                        chunks.append(chunk_text)

                    # Start new chunk with overlap
                    overlap_units = self._get_overlap_sentences(current_chunk)
                    current_chunk = overlap_units + [sentence]
                    current_tokens = sum(len(s) for s in current_chunk)
                else:
                    # Try to include more content if sentence is not too large
                    if sentence_tokens < self.max_tokens * 0.4:
                        current_chunk.append(sentence)
                        current_tokens += sentence_tokens
                    else:
                        # Must break here due to size
                        chunk_text = self._join_sentences_naturally(current_chunk)
                        if len(chunk_text) >= self.min_chunk_size:
                            chunks.append(chunk_text)

                        # Start fresh with overlap
                        overlap_units = self._get_overlap_sentences(current_chunk)
                        current_chunk = overlap_units + [sentence]
                        current_tokens = sum(len(s) for s in current_chunk)

                i += 1
            else:
                # Add to current chunk
                current_chunk.append(sentence)
                current_tokens += sentence_tokens
                i += 1

        # Add final chunk if it has content
        if current_chunk:
            chunk_text = self._join_sentences_naturally(current_chunk)
            if len(chunk_text) >= self.min_chunk_size:
                chunks.append(chunk_text)
            elif chunks:
                # If final chunk is too small, merge with previous
                chunks[-1] = chunks[-1] + "\n\n" + chunk_text

        return chunks
    
    def _is_semantic_boundary(self, current_chunk: List[str], all_sentences: List[str], next_index: int) -> bool:
        """Determine if this is a semantic boundary for chunking"""

        if not current_chunk:
            return True

        # Check if we have a complete semantic unit
        last_unit = current_chunk[-1]

        # Strong boundary indicators
        if last_unit.rstrip().endswith(('।', '?', '!')):
            if next_index < len(all_sentences):
                next_unit = all_sentences[next_index]

                # Major section changes
                section_changes = [
                    r'^(मुद्दा|निर्णय|फैसला|भाग)',  # Case/decision/section markers
                    r'^(पुनरावेदक|प्रत्यर्थी|वादी|प्रतिवादी)',  # Party changes
                    r'^(सर्वोच्च|उच्च|जिल्ला)\s+अदालत',  # Court changes
                    r'^\d+\.',  # Numbered sections
                    r'^(प्रकरण|अध्याय)',  # Chapter markers
                    r'^(सम्माननीय|माननीय)',  # Judge titles
                    r'^\d{3}-[A-Z]{2}-\d{4}',  # Case numbers
                ]

                for pattern in section_changes:
                    if re.match(pattern, next_unit.strip()):
                        return True

                # Check for paragraph breaks indicating topic change
                if len(next_unit) < 100 and self._is_structural_element(next_unit):
                    return True

            return True

        return False
    
    def _get_overlap_sentences(self, current_chunk: List[str]) -> List[str]:
        """Get sentences for overlap to maintain flow"""
        if self.overlap_sentences > 0 and len(current_chunk) > self.overlap_sentences:
            return current_chunk[-self.overlap_sentences:]
        return []
    
    def _join_sentences_naturally(self, sentences: List[str]) -> str:
        """Join sentences in a natural way"""
        if not sentences:
            return ""
        
        # Join with appropriate spacing
        result = ""
        for i, sentence in enumerate(sentences):
            if i > 0:
                # Add appropriate spacing between sentences
                if result.rstrip().endswith(('।', '?', '!')):
                    result += "\n\n" + sentence
                else:
                    result += " " + sentence
            else:
                result = sentence
        
        return result.strip()
    
    def _determine_flow_position(self, chunk_index: int, total_chunks: int) -> str:
        """Determine position in document flow"""
        if chunk_index == 0:
            return "beginning"
        elif chunk_index == total_chunks - 1:
            return "end"
        elif chunk_index < total_chunks * 0.3:
            return "early"
        elif chunk_index > total_chunks * 0.7:
            return "late"
        else:
            return "middle"
    
    def _calculate_flow_quality(self, chunks: List[str]) -> float:
        """Calculate how well the chunks preserve flow (0-1)"""
        if not chunks:
            return 0.0
        
        quality_score = 0.0
        
        # Check semantic endings
        semantic_endings = sum(1 for chunk in chunks if chunk.rstrip().endswith(('।', '?', '!')))
        quality_score += (semantic_endings / len(chunks)) * 0.4
        
        # Check chunk size consistency
        lengths = [len(chunk) for chunk in chunks]
        avg_length = sum(lengths) / len(lengths)
        size_consistency = 1 - (max(lengths) - min(lengths)) / (avg_length * 2)
        quality_score += max(0, size_consistency) * 0.3
        
        # Check for natural transitions
        natural_transitions = 0
        for i in range(len(chunks) - 1):
            if self._has_natural_transition(chunks[i], chunks[i + 1]):
                natural_transitions += 1
        
        if len(chunks) > 1:
            quality_score += (natural_transitions / (len(chunks) - 1)) * 0.3
        
        return round(quality_score, 2)
    
    def _has_natural_transition(self, chunk1: str, chunk2: str) -> bool:
        """Check if transition between chunks is natural"""
        # Check if first chunk ends well
        if not chunk1.rstrip().endswith(('।', '?', '!')):
            return False
        
        # Check if second chunk starts appropriately
        chunk2_start = chunk2.strip()[:50]
        
        # Good starting patterns
        good_starts = [
            r'^[A-Za-z\u0900-\u097F]',  # Starts with letter
            r'^\d+\.',  # Numbered point
            r'^(तसर्थ|यसैले|अतः)',  # Conclusion words
        ]
        
        for pattern in good_starts:
            if re.match(pattern, chunk2_start):
                return True
        
        return False


def create_flow_preserving_chunker(
    max_tokens: int = 1500,
    min_chunk_size: int = 500,
    overlap_sentences: int = 3
) -> FlowPreservingNepaliChunker:
    """Factory function to create a semantic chunker optimized for fewer, larger chunks"""
    return FlowPreservingNepaliChunker(
        max_tokens=max_tokens,
        min_chunk_size=min_chunk_size,
        overlap_sentences=overlap_sentences,
        preserve_flow=True
    )
