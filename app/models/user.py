from pydantic import BaseModel, Field, field_validator, ConfigDict
from typing import Any, Dict, Literal, List, Optional
from fastapi import HTTPException
import logging

from .permission import Permission

logger = logging.getLogger(__name__)

class User(BaseModel):
    """
    User document from the tenant database -> users collection
    """
    id: Any = Field(alias="_id")
    username: str
    role: str = Field(description="Single role assigned to user")
    permissions: List[Permission]


    @field_validator("id")
    def convert_objid_to_str(cls, value) -> str:
        return str(object=value)

    def has_role(self, role_name: str) -> bool:
        """Check if user has a specific role"""
        return self.role == role_name

    def has_any_role(self, role_names: List[str]) -> bool:
        """Check if user has any of the specified roles"""
        return self.role in role_names

    def is_admin(self) -> bool:
        """Check if user has admin role"""
        return self.role == "admin"

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        )

class UserTenantDB(BaseModel):
    tenant_id: str
    tenant_name: str
    read_db: Any  # Read database for all roles (AsyncDatabase)
    write_db: Any | None = None  # Write database only for admin role (AsyncDatabase)
    user: User

    model_config = ConfigDict(arbitrary_types_allowed=True)

    @property
    def db(self) -> Any:
        """Legacy property for backward compatibility - returns read_db"""
        return self.read_db

    def get_write_db(self) -> Any:
        """Get write database - raises exception if user doesn't have write access"""
        if self.write_db is None:
            logger.warning(f"Write access denied for user {self.user.username}")
            raise HTTPException(
                status_code=403,
                detail="Write access denied. Admin role required."
            )
        return self.write_db

    def has_write_access(self) -> bool:
        """Check if user has write database access"""
        return self.write_db is not None

    def get_db_for_operation(self, operation_type: str = "read") -> Any:
        """Get appropriate database based on operation type"""
        if operation_type.lower() == "write":
            return self.get_write_db()
        return self.read_db

    def log_access(self, operation: str, collection: str = "") -> None:
        """Log database access for audit purposes"""
        access_type = "write" if self.has_write_access() and operation.startswith(("insert", "update", "delete")) else "read"
        logger.info(
            f"User {self.user.username} performed {operation} on {collection} "
            f"in tenant {self.tenant_name} with {access_type} access"
        )

    
class AgentInvitation(BaseModel):
    username: str = Field(
        ...,
        json_schema_extra={"example": "agent_username"}
    )
    role: Literal["admin", "supervisor", "agent"] = Field(
        default="agent",
        description="Role to assign to the user"
    )

class AgentRegistration(BaseModel):
    username: str = Field(
        ...,
        json_schema_extra={"example": "agent_username"}
    )
    role: Literal["admin", "supervisor", "agent"] = Field(
        default="agent",
        description="Role to assign to the user"
    )
    password: str = Field(
        ...,
        json_schema_extra={"example": "strongpassword123"}
    )
    token: str = Field(
        ...,
        json_schema_extra={"example": "invitation_token_here"}
    )
