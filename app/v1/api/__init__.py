from fastapi import FastAPI, responses

from .users import router as user_router
from .config import router as config_router
from .roles import router as role_router
from .permissions import router as permissions_router
from .setup import router as setup_router
from .knowledgebase import router as knowledgebase_router


v1_api = FastAPI(
    title="API V1",
    version="1.0",
    description="Version 1 of the API",
    docs_url="/docs",
    openapi_url="/openapi.json",
    servers=[
        {"url": "/v1", "description": "Version 1"}
    ]
)

@v1_api.get("/hello")
async def hello_v1():
    return {"message": "Hello from V1"}


@v1_api.get("/", include_in_schema=False)
async def v1_root():
    return responses.RedirectResponse(url="/v1/docs")

v1_api.include_router(user_router)
v1_api.include_router(config_router)
v1_api.include_router(role_router)
v1_api.include_router(permissions_router)
v1_api.include_router(setup_router)
v1_api.include_router(knowledgebase_router)
v1_api.include_router(knowledgebase_router)
