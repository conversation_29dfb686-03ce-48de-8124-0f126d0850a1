"""
Knowledge base core business logic
"""

from typing import Dict, Any, List
from app.core.utils.response import Paginator, PaginatedResponse
from .models import FilterOptions, FindRequest


async def get_filter_options_data(collection) -> FilterOptions:
    """Core function to get filter options from database"""
    # Single aggregation to get all distinct values
    pipeline = [
        {
            "$group": {
                "_id": None,
                "court_types": {"$addToSet": "$court_type"},
                "mudda_types": {"$addToSet": "$mudda_type"},
                "months": {"$addToSet": "$month"},
                "article_ids": {"$addToSet": "$article_id"},
                "content_extracted_options": {"$addToSet": "$content_extracted"}
            }
        }
    ]
    
    result = await collection.aggregate(pipeline).to_list(1)
    
    if result:
        data = result[0]
        return FilterOptions(
            court_types=[ct for ct in data.get("court_types", []) if ct],
            mudda_types=[mt for mt in data.get("mudda_types", []) if mt],
            months=[m for m in data.get("months", []) if m],
            article_ids=[aid for aid in data.get("article_ids", []) if aid][:100],
            content_extracted_options=[ce for ce in data.get("content_extracted_options", []) if ce is not None]
        )
    else:
        return FilterOptions(
            court_types=[],
            mudda_types=[],
            months=[],
            article_ids=[],
            content_extracted_options=[]
        )


def build_search_criteria(find_request: FindRequest) -> Dict[str, Any]:
    """Build MongoDB search criteria from FindRequest"""
    criteria = {}
    
    # Text search
    if find_request.query:
        criteria["$or"] = [
            {"title": {"$regex": find_request.query, "$options": "i"}},
            {"text": {"$regex": find_request.query, "$options": "i"}},
            {"summary": {"$regex": find_request.query, "$options": "i"}},
            {"actual_cleaned_text": {"$regex": find_request.query, "$options": "i"}}
        ]
    
    # Exact match filters
    if find_request.article_id:
        criteria["article_id"] = find_request.article_id
    if find_request.court_type:
        criteria["court_type"] = find_request.court_type
    if find_request.mudda_type:
        criteria["mudda_type"] = find_request.mudda_type
    if find_request.month:
        criteria["month"] = find_request.month
    if find_request.content_extracted is not None:
        criteria["content_extracted"] = find_request.content_extracted
    if find_request.views:
        criteria["views"] = find_request.views
    
    # Date filters
    if find_request.created_at_from or find_request.created_at_to:
        date_filter = {}
        if find_request.created_at_from:
            date_filter["$gte"] = find_request.created_at_from
        if find_request.created_at_to:
            date_filter["$lte"] = find_request.created_at_to
        criteria["created_at"] = date_filter
    
    if find_request.updated_at_from or find_request.updated_at_to:
        date_filter = {}
        if find_request.updated_at_from:
            date_filter["$gte"] = find_request.updated_at_from
        if find_request.updated_at_to:
            date_filter["$lte"] = find_request.updated_at_to
        criteria["updated_at"] = date_filter
    
    return criteria


async def find_documents_data(collection, find_request: FindRequest) -> PaginatedResponse:
    """Core function to find documents with single aggregation"""
    # Build search criteria
    criteria = build_search_criteria(find_request)
    
    # Pagination
    paginator = Paginator(page=find_request.page, per_page=find_request.per_page)
    
    # Single aggregation using facet for both count and data
    combined_pipeline: List[Dict[str, Any]] = [
        {"$match": criteria},
        {
            "$facet": {
                "data": [
                    {"$sort": {find_request.sort_by or "created_at": find_request.sort_order}},
                    {"$skip": paginator.skip},
                    {"$limit": paginator.limit}
                ],
                "count": [
                    {"$count": "total"}
                ]
            }
        }
    ]
    
    result = await collection.aggregate(combined_pipeline).to_list(1)
    
    if result:
        data = result[0]
        documents = data.get("data", [])
        total = data.get("count", [{}])[0].get("total", 0)
    else:
        documents = []
        total = 0
    
    return paginator.paginate(
        data=documents,
        total=total,
        message=f"Found {total} documents"
    )
