"""
Simple Nepali Text Chunker

A straightforward chunker for Nepali text that creates semantic chunks
while preserving document relationships.
"""

import re
from typing import List
from pydantic import Field
from llama_index.core.schema import TransformComponent, TextNode


class FlowPreservingNepaliChunker(TransformComponent):
    """
    Simple semantic chunker for Nepali text.

    Features:
    - Simple semantic chunking based on Nepali punctuation
    - Preserves document_id for parent-child relationships
    - Configurable chunk sizes
    - Basic overlap for context
    """

    max_tokens: int = Field(default=1200, description="Maximum characters per chunk")
    min_chunk_size: int = Field(default=400, description="Minimum characters per chunk")
    overlap_size: int = Field(default=100, description="Characters to overlap between chunks")
    
    def __call__(self, nodes: List[TextNode]) -> List[TextNode]:
        """Process a list of nodes and return chunked nodes"""
        all_chunked_nodes = []

        for node in nodes:
            chunked_nodes = self.chunk_single_node(node)
            all_chunked_nodes.extend(chunked_nodes)

        return all_chunked_nodes

    def chunk_single_node(self, node: TextNode) -> List[TextNode]:
        """Chunk a single node into smaller pieces"""

        # Get document_id from metadata
        document_id = node.metadata.get("document_id", "unknown")

        # Simple text cleaning
        text = self._simple_clean(node.text)

        # Split into chunks
        chunks = self._create_simple_chunks(text)

        # Create chunk nodes
        chunk_nodes = []
        for i, chunk_text in enumerate(chunks):
            chunk_metadata = node.metadata.copy() if node.metadata else {}
            chunk_metadata.update({
                "document_id": document_id,  # Preserve parent document_id
                "chunk_id": i,
                "total_chunks": len(chunks),
                "chunk_size": len(chunk_text),
                "parent_node_id": node.node_id if hasattr(node, 'node_id') else None
            })

            chunk_node = TextNode(text=chunk_text, metadata=chunk_metadata)
            chunk_nodes.append(chunk_node)

        return chunk_nodes
    
    def _simple_clean(self, text: str) -> str:
        """Simple text cleaning"""
        # Basic whitespace normalization
        text = re.sub(r'[ \t]+', ' ', text)
        text = re.sub(r'\n\s*\n+', '\n\n', text)
        text = re.sub(r'&nbsp;', ' ', text)
        return text.strip()

    def _create_simple_chunks(self, text: str) -> List[str]:
        """Create simple chunks based on Nepali sentence boundaries"""

        # Split by Nepali sentence endings
        sentences = re.split(r'([।!?])', text)

        # Rebuild sentences with their punctuation
        complete_sentences = []
        i = 0
        while i < len(sentences):
            if i + 1 < len(sentences) and sentences[i + 1] in ['।', '!', '?']:
                complete_sentences.append(sentences[i] + sentences[i + 1])
                i += 2
            else:
                if sentences[i].strip():
                    complete_sentences.append(sentences[i])
                i += 1

        # Group sentences into chunks
        chunks = []
        current_chunk = ""

        for sentence in complete_sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # Check if adding this sentence would exceed max_tokens
            if len(current_chunk) + len(sentence) > self.max_tokens and current_chunk:
                # Save current chunk if it meets minimum size
                if len(current_chunk) >= self.min_chunk_size:
                    chunks.append(current_chunk.strip())

                    # Start new chunk with overlap
                    overlap_text = self._get_overlap(current_chunk)
                    current_chunk = overlap_text + " " + sentence if overlap_text else sentence
                else:
                    # Current chunk too small, just add the sentence
                    current_chunk += " " + sentence
            else:
                # Add sentence to current chunk
                if current_chunk:
                    current_chunk += " " + sentence
                else:
                    current_chunk = sentence

        # Add final chunk
        if current_chunk.strip() and len(current_chunk) >= self.min_chunk_size:
            chunks.append(current_chunk.strip())
        elif chunks and current_chunk.strip():
            # Merge small final chunk with previous
            chunks[-1] += " " + current_chunk.strip()

        return chunks
    
    def _get_overlap(self, text: str) -> str:
        """Get overlap text from the end of current chunk"""
        if len(text) <= self.overlap_size:
            return text

        # Get last overlap_size characters, but try to break at sentence boundary
        overlap_text = text[-self.overlap_size:]

        # Try to find a sentence boundary in the overlap
        for punct in ['।', '!', '?']:
            if punct in overlap_text:
                # Take from the last sentence boundary
                last_boundary = overlap_text.rfind(punct)
                if last_boundary > 0:
                    return overlap_text[last_boundary + 1:].strip()

        return overlap_text.strip()

def create_flow_preserving_chunker(
    max_tokens: int = 1200,
    min_chunk_size: int = 400,
    overlap_size: int = 100
) -> FlowPreservingNepaliChunker:
    """Factory function to create a simple Nepali text chunker"""
    return FlowPreservingNepaliChunker(
        max_tokens=max_tokens,
        min_chunk_size=min_chunk_size,
        overlap_size=overlap_size
    )
