from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any
import logging

from app.core.database import (
    setup_admin_database, 
    setup_tenant_database, 
    check_tenant_setup,
    get_write_db,
    _get_project_name
)
from app.core.security import require_admin
from app.models.user import UserTenantDB
from bson import ObjectId

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Setup"])

class TenantCreateRequest(BaseModel):
    name: str
    slug: str

class SetupResponse(BaseModel):
    success: bool
    message: str
    data: Dict[str, Any] = {}

@router.post("/setup/admin", response_model=SetupResponse)
async def setup_admin():
    """Setup admin database - can be called at startup"""
    try:
        await setup_admin_database()
        return SetupResponse(
            success=True,
            message="Admin database setup completed",
            data={"admin_db": f"{_get_project_name()}_admin"}
        )
    except Exception as e:
        logger.error(f"Admin setup failed: {e}")
        raise HTTPException(status_code=500, detail=f"Admin setup failed: {str(e)}")

@router.post("/setup/tenant", response_model=SetupResponse)
async def create_and_setup_tenant(request: TenantCreateRequest):
    """Create new tenant and setup database with dummy users"""
    try:
        # Create tenant in admin db
        admin_db_name = f"{_get_project_name()}_admin"
        admin_db = get_write_db(admin_db_name)
        
        # Check if tenant already exists
        existing_tenant = await admin_db.tenants.find_one({"slug": request.slug})
        if existing_tenant:
            return SetupResponse(
                success=False,
                message=f"Tenant with slug '{request.slug}' already exists",
                data={"tenant_id": str(existing_tenant["_id"])}
            )
        
        # Create tenant document
        clean_name = request.name.lower().replace(" ", "_")
        db_name = f"{clean_name}_db"
        
        tenant_doc = {
            "name": request.name,
            "slug": request.slug,
            "db_name": db_name,
            "is_setup": False,
            "created_at": ObjectId().generation_time,
            "status": "active"
        }
        
        result = await admin_db.tenants.insert_one(tenant_doc)
        tenant_id = str(result.inserted_id)
        
        # Setup tenant database with dummy users
        await setup_tenant_database(tenant_id)
        
        return SetupResponse(
            success=True,
            message=f"Tenant '{request.name}' created and setup completed",
            data={
                "tenant_id": tenant_id,
                "tenant_name": request.name,
                "tenant_slug": request.slug,
                "db_name": db_name,
                "dummy_users": [
                    {"username": "admin", "password": "admin123", "role": "admin"},
                    {"username": "supervisor", "password": "supervisor123", "role": "supervisor"},
                    {"username": "agent", "password": "agent123", "role": "agent"}
                ]
            }
        )
        
    except Exception as e:
        logger.error(f"Tenant setup failed: {e}")
        raise HTTPException(status_code=500, detail=f"Tenant setup failed: {str(e)}")

@router.post("/setup/tenant/{tenant_id}", response_model=SetupResponse)
async def setup_existing_tenant(
    tenant_id: str,
    current_user: UserTenantDB = Depends(require_admin())
):
    """Setup existing tenant database with dummy users (admin only)"""
    try:
        # Check if already setup
        is_setup = await check_tenant_setup(tenant_id)
        if is_setup:
            return SetupResponse(
                success=False,
                message="Tenant database is already setup",
                data={"tenant_id": tenant_id}
            )
        
        # Setup tenant database
        await setup_tenant_database(tenant_id)
        
        return SetupResponse(
            success=True,
            message="Tenant database setup completed",
            data={
                "tenant_id": tenant_id,
                "dummy_users": [
                    {"username": "admin", "password": "admin123", "role": "admin"},
                    {"username": "supervisor", "password": "supervisor123", "role": "supervisor"},
                    {"username": "agent", "password": "agent123", "role": "agent"}
                ]
            }
        )
        
    except Exception as e:
        logger.error(f"Tenant setup failed: {e}")
        raise HTTPException(status_code=500, detail=f"Tenant setup failed: {str(e)}")

@router.get("/setup/tenant/{tenant_id}/status", response_model=SetupResponse)
async def check_tenant_status(tenant_id: str):
    """Check if tenant database is setup"""
    try:
        is_setup = await check_tenant_setup(tenant_id)
        
        return SetupResponse(
            success=True,
            message=f"Tenant setup status: {'completed' if is_setup else 'pending'}",
            data={
                "tenant_id": tenant_id,
                "is_setup": is_setup
            }
        )
        
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Status check failed: {str(e)}")

@router.get("/setup/tenants", response_model=SetupResponse)
async def list_tenants():
    """List all tenants with setup status"""
    try:
        admin_db_name = f"{_get_project_name()}_admin"
        admin_db = get_write_db(admin_db_name)
        
        tenants = []
        async for tenant in admin_db.tenants.find({}):
            tenants.append({
                "tenant_id": str(tenant["_id"]),
                "name": tenant["name"],
                "slug": tenant["slug"],
                "db_name": tenant["db_name"],
                "is_setup": tenant.get("is_setup", False),
                "status": tenant.get("status", "unknown"),
                "created_at": tenant.get("created_at")
            })
        
        return SetupResponse(
            success=True,
            message=f"Found {len(tenants)} tenants",
            data={"tenants": tenants}
        )
        
    except Exception as e:
        logger.error(f"List tenants failed: {e}")
        raise HTTPException(status_code=500, detail=f"List tenants failed: {str(e)}")
