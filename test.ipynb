# Setup MongoDB connection
from pymongo import AsyncMongoClient
import os

uri = os.getenv("MONGO_URI")
client = AsyncMongoClient(uri)
db = client["legal_backend_db"]
collection = db["documents"]

print("MongoDB connection established")

doc=await collection.find().to_list()
doc=doc[:10]
for d in doc:
    d["metadata"]["year"]=d["year"]
    d["metadata"]["document_id"]=str(d["_id"])

from llama_index.core.schema import TextNode
llama_docs=[TextNode(text=d["text"],metadata=d["metadata"]) for d in doc]

llama_docs

# Import the chunker from the proper module
from app.v1.api.knowledgebase.core.chunking.flow_preserving_chunker import FlowPreservingNepaliChunker, create_flow_preserving_chunker


# Create the chunker instance
chunker = create_flow_preserving_chunker(
        max_tokens=800,
        min_chunk_size=300,  # Reduced to handle smaller documents
        overlap_size=80
    )


print(f"Created chunker with max_tokens={chunker.max_tokens}, min_chunk_size={chunker.min_chunk_size}")

chunked=chunker(llama_docs)

len(chunked)

from llama_index.embeddings.openai import OpenAIEmbedding
from qdrant_client import QdrantClient,AsyncQdrantClient
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import VectorStoreIndex
from llama_index.core import StorageContext


aclient = AsyncQdrantClient(
    host=os.getenv("QDRANT_HOST", "localhost"),
    port=int(os.getenv("QDRANT_PORT", 6333)),
    prefer_grpc=True
)    
client = QdrantClient(
    host=os.getenv("QDRANT_HOST", "localhost"),
    port=int(os.getenv("QDRANT_PORT", 6333)),
    prefer_grpc=True
)

from qdrant_client.models import VectorParams, Distance
# Create the collection if it does not exist
exists = await aclient.get_collection("legal_sentence_split")
if  exists:

    print("Collection 'legal_sentence_split' is ready")
else:
    print("Creating collection 'legal_sentence_split'")
    await aclient.create_collection(
        collection_name="legal_sentence_split",
        vectors_config=VectorParams(size=1536, distance=Distance.COSINE))


vector_store = QdrantVectorStore(client=client, collection_name="legal_sentence_split")
storage_context = StorageContext.from_defaults(vector_store=vector_store)
new_index = VectorStoreIndex.from_vector_store(vector_store, embed_model=OpenAIEmbedding(api_key=os.getenv("OPENAI_API_KEY"),model="text-embedding-3-large", dimensions=1536))
new_index.insert_nodes(chunked)

