# Setup MongoDB connection
from pymongo import AsyncMongoClient
import os

uri = os.getenv("MONGO_URI")
client = AsyncMongoClient(uri)
db = client["legal_backend_db"]
collection = db["documents"]

print("MongoDB connection established")

doc=await collection.find().to_list()
doc=doc[:1]
for d in doc:
    d["metadata"]["year"]=d["year"]
    d["metadata"]["document_id"]=str(d["_id"])

from llama_index.core.schema import TextNode
llama_docs=[TextNode(text=d["text"],metadata=d["metadata"]) for d in doc]

llama_docs

# Import the chunker from the proper module
from app.core.chunking.flow_preserving_chunker import FlowPreservingNepaliChunker, create_flow_preserving_chunker

# Create the chunker instance
chunker = create_flow_preserving_chunker(
    max_tokens=800,
    min_chunk_size=200,
    overlap_sentences=2
)

print(f"Created chunker with max_tokens={chunker.max_tokens}, min_chunk_size={chunker.min_chunk_size}")

# Apply chunking to the first document
if llama_docs:
    first_doc = llama_docs[0]
    print(f"Original document length: {len(first_doc.text)} characters")
    print(f"Document metadata: {first_doc.metadata}")
    print("\n" + "="*50 + "\n")
    
    # Apply chunking
    chunked_nodes, stats = chunker(first_doc)
    
    print(f"Chunking Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print(f"\nCreated {len(chunked_nodes)} chunks")
else:
    print("No documents available for chunking")

# Display the first few chunks to see the results
if 'chunked_nodes' in locals() and chunked_nodes:
    print("First 3 chunks:")
    print("="*60)
    
    for i, node_with_score in enumerate(chunked_nodes[:3]):
        node = node_with_score.node
        print(f"\nChunk {i+1}:")
        print(f"Length: {len(node.text)} characters")
        print(f"Flow position: {node.metadata.get('flow_position', 'unknown')}")
        print(f"Chunk ID: {node.metadata.get('chunk_id', 'unknown')}")
        print(f"Text preview: {node.text[:200]}...")
        print("-" * 40)
else:
    print("No chunks available to display")

# Analyze chunk quality and characteristics
if 'chunked_nodes' in locals() and chunked_nodes:
    chunk_lengths = [len(node.node.text) for node in chunked_nodes]
    
    print("Chunk Analysis:")
    print(f"Total chunks: {len(chunked_nodes)}")
    print(f"Average chunk length: {sum(chunk_lengths) / len(chunk_lengths):.1f} characters")
    print(f"Min chunk length: {min(chunk_lengths)} characters")
    print(f"Max chunk length: {max(chunk_lengths)} characters")
    
    # Check flow positions
    flow_positions = [node.node.metadata.get('flow_position', 'unknown') for node in chunked_nodes]
    from collections import Counter
    position_counts = Counter(flow_positions)
    
    print("\nFlow position distribution:")
    for position, count in position_counts.items():
        print(f"  {position}: {count} chunks")
    
    # Check semantic boundaries
    semantic_endings = sum(1 for node in chunked_nodes 
                          if node.node.text.rstrip().endswith(('।', '?', '!')))
    print(f"\nChunks ending with semantic boundaries: {semantic_endings}/{len(chunked_nodes)} ({semantic_endings/len(chunked_nodes)*100:.1f}%)")
else:
    print("No chunks available for analysis")

# Test with different chunker settings
if llama_docs:
    print("Testing different chunker configurations:")
    print("="*50)
    
    configs = [
        {"max_tokens": 500, "min_chunk_size": 150, "overlap_sentences": 1, "name": "Small chunks"},
        {"max_tokens": 1200, "min_chunk_size": 300, "overlap_sentences": 3, "name": "Large chunks"},
        {"max_tokens": 800, "min_chunk_size": 200, "overlap_sentences": 0, "name": "No overlap"}
    ]
    
    first_doc = llama_docs[0]
    
    for config in configs:
        test_chunker = create_flow_preserving_chunker(
            max_tokens=config["max_tokens"],
            min_chunk_size=config["min_chunk_size"],
            overlap_sentences=config["overlap_sentences"]
        )
        
        test_chunks, test_stats = test_chunker(first_doc)
        
        print(f"\n{config['name']} (max_tokens={config['max_tokens']}, overlap={config['overlap_sentences']}):")
        print(f"  Chunks created: {test_stats['chunks_created']}")
        print(f"  Avg tokens per chunk: {test_stats['avg_tokens_per_chunk']}")
        print(f"  Flow quality: {test_stats['flow_quality']}")
        print(f"  Semantic boundaries: {test_stats['semantic_boundaries']}")
else:
    print("No documents available for testing")

