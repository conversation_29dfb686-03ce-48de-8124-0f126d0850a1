#!/usr/bin/env python3
"""
Test script for the improved FlowPreservingNepaliChunker
Demonstrates semantic chunking with the article.txt file
"""

import os
from llama_index.core.schema import TextNode
from app.core.chunking.flow_preserving_chunker import create_flow_preserving_chunker

def load_article():
    """Load the article.txt file"""
    article_path = "app/core/chunking/article.txt"
    
    if not os.path.exists(article_path):
        print(f"Article file not found at {article_path}")
        return None
    
    with open(article_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    return TextNode(
        text=content,
        metadata={
            "source": "article.txt",
            "document_type": "legal_decision",
            "language": "nepali",
            "case_number": "10164"
        }
    )

def test_chunker():
    """Test the chunker with different configurations"""
    
    # Load the article
    article_node = load_article()
    if not article_node:
        return
    
    print(f"Original document length: {len(article_node.text)} characters")
    print(f"Document preview: {article_node.text[:200]}...")
    print("=" * 80)
    
    # Test configurations
    configs = [
        {
            "name": "Default (Fewer Chunks)",
            "max_tokens": 1500,
            "min_chunk_size": 500,
            "overlap_sentences": 3
        },
        {
            "name": "Medium Chunks",
            "max_tokens": 1000,
            "min_chunk_size": 300,
            "overlap_sentences": 2
        },
        {
            "name": "Large Chunks",
            "max_tokens": 2000,
            "min_chunk_size": 800,
            "overlap_sentences": 4
        }
    ]
    
    for config in configs:
        print(f"\n{config['name']} Configuration:")
        print(f"Max tokens: {config['max_tokens']}, Min size: {config['min_chunk_size']}, Overlap: {config['overlap_sentences']}")
        print("-" * 60)
        
        # Create chunker
        chunker = create_flow_preserving_chunker(
            max_tokens=config['max_tokens'],
            min_chunk_size=config['min_chunk_size'],
            overlap_sentences=config['overlap_sentences']
        )
        
        # Apply chunking
        chunked_nodes, stats = chunker(article_node)
        
        # Display statistics
        print(f"Statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        print(f"\nCreated {len(chunked_nodes)} chunks")
        
        # Show first few chunks
        print(f"\nFirst 2 chunks preview:")
        for i, node_with_score in enumerate(chunked_nodes[:2]):
            node = node_with_score.node
            print(f"\nChunk {i+1}:")
            print(f"  Length: {len(node.text)} characters")
            print(f"  Flow position: {node.metadata.get('flow_position', 'unknown')}")
            print(f"  Preview: {node.text[:150]}...")
            if node.text.rstrip().endswith(('।', '?', '!')):
                print(f"  ✓ Ends with semantic boundary")
            else:
                print(f"  ⚠ No semantic boundary at end")
        
        print("=" * 80)

def analyze_chunk_quality(chunked_nodes):
    """Analyze the quality of chunks"""
    if not chunked_nodes:
        return
    
    print("\nChunk Quality Analysis:")
    print("-" * 40)
    
    chunk_lengths = [len(node.node.text) for node in chunked_nodes]
    semantic_endings = sum(1 for node in chunked_nodes 
                          if node.node.text.rstrip().endswith(('।', '?', '!')))
    
    print(f"Total chunks: {len(chunked_nodes)}")
    print(f"Average length: {sum(chunk_lengths) / len(chunk_lengths):.1f} characters")
    print(f"Min length: {min(chunk_lengths)} characters")
    print(f"Max length: {max(chunk_lengths)} characters")
    print(f"Semantic endings: {semantic_endings}/{len(chunked_nodes)} ({semantic_endings/len(chunked_nodes)*100:.1f}%)")
    
    # Flow position distribution
    from collections import Counter
    flow_positions = [node.node.metadata.get('flow_position', 'unknown') for node in chunked_nodes]
    position_counts = Counter(flow_positions)
    
    print(f"\nFlow position distribution:")
    for position, count in position_counts.items():
        print(f"  {position}: {count} chunks")

if __name__ == "__main__":
    print("Testing FlowPreservingNepaliChunker with article.txt")
    print("=" * 80)
    
    test_chunker()
    
    # Test with default configuration for detailed analysis
    print("\nDetailed Analysis with Default Configuration:")
    print("=" * 80)
    
    article_node = load_article()
    if article_node:
        chunker = create_flow_preserving_chunker()
        chunked_nodes, stats = chunker(article_node)
        analyze_chunk_quality(chunked_nodes)
        
        print(f"\nSample chunk content (Chunk 1):")
        print("-" * 40)
        if chunked_nodes:
            sample_chunk = chunked_nodes[0].node.text
            print(sample_chunk[:500] + "..." if len(sample_chunk) > 500 else sample_chunk)
