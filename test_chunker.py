#!/usr/bin/env python3
"""
Test script for the simplified FlowPreservingNepaliChunker
Demonstrates chunking with document_id preservation
"""

import os
from llama_index.core.schema import TextNode
from app.core.chunking.flow_preserving_chunker import create_flow_preserving_chunker

def create_sample_nodes():
    """Create sample nodes with document_ids"""

    # Load article.txt if available
    article_path = "app/core/chunking/article.txt"
    if os.path.exists(article_path):
        with open(article_path, 'r', encoding='utf-8') as f:
            article_text = f.read()
    else:
        # Fallback sample text
        article_text = """निर्णय नं. १०१६४ - निर्णय दर्ता बदर

भाग: ६१ साल: २०७६ महिना: बैशाख अंक: १

सर्वोच्च अदालत, संयुक्त इजलास

सम्माननीय प्रधानन्यायाधीश श्री गोपाल पराजुली

माननीय न्यायाधीश श्री केदारप्रसाद चालिसे

मुद्दा:- निर्णय दर्ता बदर

पुनरावेदक / प्रतिवादी : काठमाडौं जिल्ला, कपन गा.वि.स. वडा नं. ३ बस्ने कान्छी तामाङसमेत

विरूद्ध

प्रत्यर्थी / वादी : का.जि.का.म.न.पा. वडा नं. २३ बस्ने नातीभाइ गुभाजु भन्ने नाति शाक्यकी श्रीमती रत्नमाया शाक्य

विबन्धनको सिद्धान्त हक खानेको हकमा पनि समान रूपमा आकर्षित हुने हुँदा बाबुले स्वीकार गरेको तथ्यलार्इ इन्कार गर्न विबन्धनको सिद्धान्तले यी प्रतिवादीहरूलार्इ समेत रोक्ने ।"""

    # Create sample nodes
    nodes = [
        TextNode(
            text=article_text,
            metadata={
                "document_id": "doc_001",
                "title": "निर्णय दर्ता बदर",
                "court": "सर्वोच्च अदालत",
                "year": "2076"
            }
        ),
        TextNode(
            text="""यो दोस्रो कागजात हो । यसमा फरक विषयवस्तु छ । न्यायालयको निर्णय अनुसार यो मुद्दा समाधान भएको छ । पक्षहरूले यस निर्णयलाई मान्नुपर्छ ।

अदालतको फैसला अनुसार वादीको दाबी पुग्ने देखिएको छ । प्रतिवादीले यस निर्णयको विरुद्धमा पुनरावेदन गर्न सक्नेछ । तर निर्धारित समयसीमाभित्र पुनरावेदन गर्नुपर्नेछ ।

यस मुद्दामा दुवै पक्षका वकिलहरूले आ-आफ्ना तर्कहरू प्रस्तुत गरेका थिए । अदालतले सबै प्रमाणहरूको विश्लेषण गरेर यो निर्णयमा पुगेको छ । यो निर्णय न्यायसंगत र कानूनसम्मत छ ।""",
            metadata={
                "document_id": "doc_002",
                "title": "दोस्रो निर्णय",
                "court": "जिल्ला अदालत",
                "year": "2077"
            }
        )
    ]

    return nodes

def test_chunker():
    """Test the simplified chunker"""

    # Create sample nodes
    nodes = create_sample_nodes()

    print("Testing Simple Nepali Text Chunker")
    print("=" * 60)

    # Create chunker with smaller minimum size to handle both documents
    chunker = create_flow_preserving_chunker(
        max_tokens=800,
        min_chunk_size=200,  # Reduced to handle smaller documents
        overlap_size=80
    )

    print(f"Chunker settings:")
    print(f"  Max tokens: {chunker.max_tokens}")
    print(f"  Min chunk size: {chunker.min_chunk_size}")
    print(f"  Overlap size: {chunker.overlap_size}")
    print()

    # Process all nodes
    all_chunks = chunker(nodes)

    print(f"Input: {len(nodes)} documents")
    print(f"Output: {len(all_chunks)} chunks")
    print()

    # Group chunks by document_id
    chunks_by_doc = {}
    for chunk in all_chunks:
        doc_id = chunk.node.metadata.get("document_id", "unknown")
        if doc_id not in chunks_by_doc:
            chunks_by_doc[doc_id] = []
        chunks_by_doc[doc_id].append(chunk)

    # Display results
    for doc_id, doc_chunks in chunks_by_doc.items():
        # Find original node
        original_node = None
        for node in nodes:
            if node.metadata.get("document_id") == doc_id:
                original_node = node
                break

        print(f"Document ID: {doc_id}")
        print(f"  Original metadata: {original_node.metadata if original_node else 'Not found'}")
        print(f"  Original length: {len(original_node.text) if original_node else 'Unknown'} characters")
        print(f"  Number of chunks: {len(doc_chunks)}")

        for i, chunk in enumerate(doc_chunks):
            node = chunk.node
            print(f"  Chunk {i+1}:")
            print(f"    Document ID: {node.metadata.get('document_id')}")
            print(f"    Chunk ID: {node.metadata.get('chunk_id')}")
            print(f"    Size: {len(node.text)} characters")
            print(f"    Preview: {node.text[:100]}...")
            print(f"    Ends with punctuation: {node.text.rstrip().endswith(('।', '?', '!'))}")
        print("-" * 40)

if __name__ == "__main__":
    test_chunker()
